'use client';

import React from 'react';
import { CsrAdpModalProvider, useCsrAdpModal } from './csr-adp-modal-context';
import CsrAdpModalContent from './csr-adp-modal-content';

const CsrAdpModalInner: React.FC = () => {
  const { selectedCustomer } = useCsrAdpModal();
  
  return <CsrAdpModalContent selectedCustomer={selectedCustomer} />;
};

export const CsrAdpModalWrapper: React.FC = () => {
  return (
    <CsrAdpModalProvider>
      <CsrAdpModalInner />
    </CsrAdpModalProvider>
  );
};

export default CsrAdpModalWrapper;
