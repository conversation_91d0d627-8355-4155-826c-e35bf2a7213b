import { type NextRequest } from 'next/server';
import { createChildLogger } from 'logger';
import { ENTRY, EXIT, ERROR } from 'constants/logging';
import { getRequestId } from 'utils/requestId';
import { getEsite } from 'services/esite';
import { getUser } from 'services/user';
import { err as serializer } from 'pino-std-serializers';
import { Method } from 'constants/enums';
import { PRE_SELECT_STORE } from 'constants/endpoints';
import { getFormattedURL } from 'utils/url';
import { ECOMMERCE_DEVELOPMENT_ROOT } from 'constants/links';
import { getWCCookies, updateCookies } from 'utils/cookies';
import { selectAuthorizationHeader } from 'utils/authorizationHeader';
import { REQUEST_ID_HEADER } from 'constants/headers';

export async function POST(req: NextRequest) {
  const pccRequestId = getRequestId(req.headers);
  
  const childLogger = createChildLogger({
    msgPrefix: 'Dealer Customer Org Session Handler',
    pccRequestId
  });

  childLogger.trace(ENTRY);

  try {
    const body = await req.json();
    const { onBehalfofUserId } = body;
    
    const { storeId } = await getEsite();
    const user = await getUser();
    
    if (!user?.userAccessFlags?.isCatCsrUser) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized: CSR access required' }), 
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Call the dealercustomerOrgSetInSesion endpoint
    const dealerCustomerOrgUrl = getFormattedURL(
      process.env.COMMERCE_API_HOST ?? '',
      `${ECOMMERCE_DEVELOPMENT_ROOT}${PRE_SELECT_STORE}`,
      { storeId }
    );

    childLogger.debug({ dealerCustomerOrgUrl, onBehalfofUserId });

    const response = await fetch(dealerCustomerOrgUrl, {
      method: Method.GET,
      headers: {
        Authorization: await selectAuthorizationHeader(),
        [REQUEST_ID_HEADER]: pccRequestId,
        cookie: await getWCCookies()
      }
    });

    if (!response.ok) {
      throw new Error(`Dealer customer org session API responded with status: ${response.status}`);
    }

    // Update cookies from the response
    const newCookies = response.headers.getSetCookie();
    if (newCookies && newCookies.length > 0) {
      updateCookies(newCookies);
    }

    // Get user name from user details for redirection
    const userName = user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`
      : user.logonId || 'User';

    // Prepare redirect URL to dealer esite
    const dealerInfo = user.userDealerSelectionInfo;
    const redirectUrl = dealerInfo?.dealer 
      ? `/${dealerInfo.dealer}/home`
      : '/home';

    const result = {
      success: true,
      message: 'CSR taken to the dealer eSite',
      userName,
      redirectUrl
    };

    childLogger.trace(EXIT);
    return Response.json(result);
  } catch (error) {
    childLogger.error(serializer(error), ERROR);
    childLogger.trace(EXIT);
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to set dealer customer organization in session',
        message: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
