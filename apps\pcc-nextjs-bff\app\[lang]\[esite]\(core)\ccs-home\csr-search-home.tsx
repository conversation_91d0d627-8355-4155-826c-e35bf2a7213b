'use client';

import {
  CatTab,
  CatTabPanel,
  CatTabs,
  CatInlineNotification,
  CatIconWarningDiamondFilled
} from 'components/blocks-components';
import { useTranslations } from 'next-intl';
import { useState, memo, useEffect } from 'react';
import CustomerSearchForm from './customer-search-form/customer-search-form';
import OrderSearchForm from './order-search-from';
import { CountryType } from 'services/countries-and-states/types';
import { useCsrGaEventsError } from './hooks/csr-ga-events';
import { FILL_SEARCH_FIELDS } from 'constants/analyticsConstants';

interface CSRSearchFormsProps {
  countries: CountryType[];
  isCsrUser?: boolean;
}

const CSRSearchForms = ({
  countries = [],
  isCsrUser = false
}: CSRSearchFormsProps) => {
  const t = useTranslations();
  const [activeIndex, setActiveIndex] = useState(0);
  const [showEmptyFieldsError, setShowEmptyFieldsError] = useState(false);

  const { fireCsrPageEvent } = useCsrGaEventsError();

  useEffect(() => {
    if (showEmptyFieldsError) {
      fireCsrPageEvent(FILL_SEARCH_FIELDS);
    }
  }, [fireCsrPageEvent, showEmptyFieldsError]);

  return (
    <div className="mt-4">
      {showEmptyFieldsError && (
        <CatInlineNotification variant="error" className="mb-4">
          <CatIconWarningDiamondFilled slot="before" />
          {t('MISSING_FIELDS')}
          <p>{t('FILL_SEARCH_FIELDS')}</p>
        </CatInlineNotification>
      )}

      <CatTabs
        behavior="full-width"
        activeIndex={activeIndex}
        onBlTabChange={(e) => {
          setActiveIndex(e.detail.activeTabIndex);
          setShowEmptyFieldsError(false);
        }}
      >
        <CatTab>{t('CUSTOMER_SEARCH')}</CatTab>
        <CatTabPanel slot="panel" className="mt-4">
          <CustomerSearchForm
            countries={countries}
            onErrorChange={setShowEmptyFieldsError}
            isCsrUser={isCsrUser}
          />
        </CatTabPanel>

        <CatTab>{t('ORDER_SEARCH')}</CatTab>
        <CatTabPanel slot="panel" className="mt-4">
          <OrderSearchForm onErrorChange={setShowEmptyFieldsError} />
        </CatTabPanel>
      </CatTabs>
    </div>
  );
};

export default memo(CSRSearchForms);
