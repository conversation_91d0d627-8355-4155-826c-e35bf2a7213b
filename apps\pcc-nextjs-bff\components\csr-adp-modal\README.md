# CSR ADP Modal Implementation

This implementation provides a complete CSR (Customer Service Representative) ADP (Associated Dealer Program) modal feature that allows CSR users to connect to customer accounts by clicking on customer logonIds in the customer search table.

## Features Implemented

### 1. CSR ADP Modal Component
- **File**: `csr-adp-modal-content.tsx`
- **Features**:
  - Modal with close icon and proper accessibility
  - Displays customer information and dealer associations
  - Shows potential account issues as warnings
  - Connect and Disconnect functionality
  - Loading states and error handling

### 2. Context and State Management
- **File**: `csr-adp-modal-context.tsx`
- **Features**:
  - React Context for modal state management
  - API integration for dealer associations and customer info
  - ADP submission and session management
  - Error handling and loading states

### 3. API Integration
- **Files**: 
  - `app/api/dealer-associations/route.ts`
  - `app/api/submit-adp/route.ts`
  - `app/api/dealer-customer-org-session/route.ts`
- **Features**:
  - Fetch dealer associations with `onBehalfofUserId` parameter
  - Submit ADP for CSR flow with proper authentication
  - Handle dealer customer organization session setup
  - Reuse existing user reset session API for disconnect

### 4. Customer Search Table Enhancement
- **File**: `customer-search-table.tsx`
- **Features**:
  - Clickable logonId for CSR users
  - Proper accessibility with keyboard navigation
  - Visual styling for clickable elements
  - Integration with CSR ADP modal context

### 5. Translation Keys
- **File**: `translation-keys.ts`
- **Keys Added**:
  - `CSR_ADP_MODAL_TITLE`
  - `CSR_ADP_MODAL_DESCRIPTION`
  - `CSR_ADP_POTENTIAL_ACCOUNT_ISSUES`
  - `CSR_ADP_DEALER_ASSOCIATIONS`
  - `CSR_ADP_CUSTOMER_INFO`
  - `CSR_ADP_CONNECT`/`CSR_ADP_DISCONNECT`
  - And more...

## API Flow

### 1. Display Flow
1. CSR clicks on customer logonId
2. Modal opens and fetches:
   - `/api/dealer-associations?onBehalfofUserId={userId}&isCsrFlow=true`
   - `/api/dealer-customers/{customerNumber}`
3. Display dealer associations and potential account issues
4. Show customer information

### 2. Submit Flow
1. CSR clicks "Connect" button
2. Submit ADP: `POST /api/submit-adp`
3. Set dealer customer org in session: `POST /api/dealer-customer-org-session`
4. Redirect to dealer esite

### 3. Disconnect Flow
1. CSR clicks "Disconnect" button
2. Call user reset session: `GET /api/user-reset-session`
3. Redirect to CSR home page

## Integration Points

### CSR Home Page
- Added `CsrAdpModalProvider` wrapper
- Passed `isCsrUser` prop through component hierarchy
- Included translation keys in TranslationsProvider

### Customer Search Components
- Enhanced table with clickable logonId
- Added proper TypeScript interfaces
- Integrated with modal context

## Usage

```tsx
// The modal is automatically available when the CsrAdpModalProvider is wrapped around components
<CsrAdpModalProvider>
  <CustomerSearchForm isCsrUser={true} />
  <CsrAdpModalWrapper />
</CsrAdpModalProvider>
```

## API Endpoints Used

1. **Dealer Associations**: `/eCommerce/me/dealerAssociations?storeId={storeId}&locale={locale}&onBehalfofUserId={userId}&isCsrFlow=true`
2. **Dealer Customer Info**: `/eCommerce/me/dealerCustomers/{customerNumber}?storeId={storeId}&locale={locale}`
3. **Submit ADP**: `/eCommerce/me/v2/entitlements?storeId={storeId}&locale={locale}`
4. **Dealer Customer Org Session**: `/dealercustomerOrgSetInSesion?storeId={storeId}`
5. **User Reset Session**: `/userResetInSession`

## Security

- All API calls require CSR user authentication
- Proper authorization checks in API routes
- CSRF protection through existing middleware
- Input validation for user IDs and parameters

## Accessibility

- Keyboard navigation support
- Proper ARIA labels and roles
- Focus management
- Screen reader compatible

## Error Handling

- API error responses with proper status codes
- User-friendly error messages
- Loading states during API calls
- Graceful fallbacks for missing data
