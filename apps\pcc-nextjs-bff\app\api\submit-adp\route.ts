import { type NextRequest } from 'next/server';
import { createChildLogger } from 'logger';
import { ENTRY, EXIT, ERROR } from 'constants/logging';
import { getRequestId } from 'utils/requestId';
import { getEsite } from 'services/esite';
import { getUser } from 'services/user';
import { err as serializer } from 'pino-std-serializers';
import { Method } from 'constants/enums';
import { SUBMIT_ADP } from 'constants/endpoints';
import { getFormattedCommerceURL } from 'utils/url.server';
import { commerceUserApiFetch } from 'utils/commerceUserApiFetch';

export async function POST(req: NextRequest) {
  const pccRequestId = getRequestId(req.headers);
  
  const childLogger = createChildLogger({
    msgPrefix: 'Submit ADP CSR Handler',
    pccRequestId
  });

  childLogger.trace(ENTRY);

  try {
    const body = await req.json();
    const { onBehalfofUserId, isCsrFlow } = body;
    
    const { storeId, locale } = await getEsite();
    const user = await getUser();
    
    if (!user?.userAccessFlags?.isCatCsrUser) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized: CSR access required' }), 
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Build the ADP submission payload for CSR
    const adpPayload = {
      isCsrFlow: true,
      onBehalfofUserId,
      // Add any additional required fields for CSR ADP submission
      storeLocationCode: user.userDealerSelectionInfo?.dealer || '',
    };

    const apiUrl = getFormattedCommerceURL(SUBMIT_ADP, {
      storeId,
      locale
    });

    childLogger.debug({ apiUrl, adpPayload });

    const response = await commerceUserApiFetch({
      url: apiUrl,
      method: Method.POST,
      body: JSON.stringify(adpPayload),
      msgPrefix: 'Submit ADP CSR',
      pccRequestId,
      headers: {
        'Content-Type': 'application/json',
        'WCToken': 'WCToken',
        'WCTrustedToken': 'WCTrustedToken'
      }
    });

    childLogger.trace(EXIT);
    return Response.json(response);
  } catch (error) {
    childLogger.error(serializer(error), ERROR);
    childLogger.trace(EXIT);
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to submit ADP',
        message: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
