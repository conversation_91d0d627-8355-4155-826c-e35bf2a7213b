'use client';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>utton, CatInputField } from 'components/blocks-components';
import { useTranslations } from 'next-intl';
import { FormProvider, useForm, Controller } from 'react-hook-form';
import { CountryType } from 'services/countries-and-states/types';
import { useCustomerSearch } from '../use-customer-search';
import { CustomerSearchResults } from './customer-search-results';
import { CountryInput } from '../_components/country/country-input';
import { CustomerSearchFormState, SORT_DIRECTIONS, SORT_TYPE } from './types';
import { BANNER_TOAST_MESSAGES_SHOWED } from '../../_global-toast/constants';
import OrderSearchTableLoader from '../order-search-from/order-search-table-loader';
import { ErrorFeedBackComponent } from '../error-feedback-component/error-feedback-component';
import { getErrorCodeFromError } from '../../account/(with-nav)/invoices/_utils/error-code-extractor';

interface CustomerSearchFormProps {
  countries?: CountryType[];
  onErrorChange: (show: boolean) => void;
  isCsrUser?: boolean;
}

const CustomerSearchForm = ({
  countries = [],
  onErrorChange,
  isCsrUser = false
}: CustomerSearchFormProps) => {
  const t = useTranslations();
  const [isRefiningSearch, setIsRefiningSearch] = useState(false);
  const autoCompletedInputs = useRef({ country: '' });
  const removeSpecialCharacterFeatureFlag = true;
  const [hasSubmittedSearch, setHasSubmittedSearch] = useState(false);

  // Use the customer search hook to get all the necessary search functionality
  const {
    searchResults,
    isSearching,
    totalCount,
    currentPage,
    pageSize,
    performSearch,
    performSortedSearch,
    clearResults,
    setCurrentPage,
    setPageSize,
    performPaginationSearch,
    error
  } = useCustomerSearch();

  const form = useForm<CustomerSearchFormState>({
    defaultValues: {
      firstName: '',
      lastName: '',
      logonId: '',
      companyName: '',
      email: '',
      phone: '',
      city: '',
      state: '',
      zip: '',
      country: '',
      countrySearch: ''
    },
    mode: 'onChange'
  });

  const { control, reset, setValue, handleSubmit } = form;

  const normalizePhoneNumber = (phoneNumber: string) => {
    return removeSpecialCharacterFeatureFlag
      ? phoneNumber.replace(/[^\d]/g, '')
      : phoneNumber;
  };

  const onUpdateAutoCompletedInputs = useCallback(
    (fieldName: string, { code }: { code: string }) => {
      const currentValues = autoCompletedInputs.current;
      autoCompletedInputs.current = {
        ...currentValues,
        [fieldName]: code ?? currentValues[fieldName]
      };
    },
    []
  );

  const handleEmptyError = (val: boolean) => {
    if (onErrorChange) {
      onErrorChange(val);
    }
  };
  const errorCode = getErrorCodeFromError(error);

  const handleSort = useCallback(
    (sortBy: string, sortType: SORT_TYPE, sortDirection: SORT_DIRECTIONS) => {
      const mapSortField = (field: string) => {
        const mapping = {
          firstName: 'fullName',
          logonId: 'loginid',
          phoneNumber: 'phone',
          emailAddress: 'email',
          company: 'company',
          profileAddress: 'address',
          lastActive: 'lastActiveDate'
        };
        return mapping[field] || field;
      };
      const apiSortBy = mapSortField(sortBy);
      const apiOrderBy = sortDirection === SORT_DIRECTIONS.ASC ? 'asc' : 'desc';

      performSortedSearch(apiSortBy, apiOrderBy);
    },
    [performSortedSearch]
  );

  const onSubmit = async (data: CustomerSearchFormState) => {
    const normalizedPhone = normalizePhoneNumber(data.phone);
    setValue('phone', normalizedPhone, { shouldDirty: true });
    const formData = {
      ...data,
      phone: normalizedPhone,
      country: autoCompletedInputs.current.country || data.country
    };

    const hasValue = Object.values(formData).some(
      (value) => value && value.toString().trim().length > 0
    );

    if (!hasValue) {
      handleEmptyError(true);
      return;
    }

    handleEmptyError(false);
    await performSearch(formData, isRefiningSearch);
    setHasSubmittedSearch(true);
  };

  const resetForm = () => {
    reset();
    clearResults();
    setIsRefiningSearch(false);
    autoCompletedInputs.current = { country: '' };
    setHasSubmittedSearch(false);
  };

  const handleSelectCountry = useCallback(
    (country: CountryType) => {
      setValue('country', country.code, { shouldDirty: true });
      setValue('countrySearch', country.displayName, { shouldDirty: true });
      onUpdateAutoCompletedInputs('country', { code: country.code });
    },
    [setValue, onUpdateAutoCompletedInputs]
  );
  //remove session storage for CSR global toast message
  useEffect(() => {
    sessionStorage.removeItem(BANNER_TOAST_MESSAGES_SHOWED);
  }, []);

  return (
    <>
      <FormProvider {...form}>
        <form onSubmit={handleSubmit(onSubmit)} className="pb-3">
          <div className="row g-3">
            <div className="col-xs-12 col-md-6">
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <CatInputField
                    type="text"
                    label={t('MUSREG_FNAME')}
                    name="firstName"
                    value={field.value}
                    onBlChange={(e) => field.onChange(e.detail.value)}
                    className="mb-3"
                    size="md"
                    data-testid="firstName"
                  />
                )}
              />
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <CatInputField
                    type="text"
                    label={t('MUSREG_LNAME')}
                    name="lastName"
                    value={field.value}
                    onBlChange={(e) => field.onChange(e.detail.value)}
                    className="mb-3"
                    size="md"
                    data-testid="lastName"
                  />
                )}
              />
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <CatInputField
                    type="text"
                    label={t('Email address')}
                    name="email"
                    value={field.value}
                    onBlChange={(e) => field.onChange(e.detail.value)}
                    className="mb-3"
                    size="md"
                    data-testid="email"
                  />
                )}
              />
            </div>
            <div className="col-xs-12 col-md-6">
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <CatInputField
                    type="tel"
                    label={t('BUY_APP_DETAIL_PHONE')}
                    name="phone"
                    value={field.value}
                    onBlChange={(e) => field.onChange(e.detail.value)}
                    className="mb-3"
                    size="md"
                    data-testid="phone"
                  />
                )}
              />
              <Controller
                name="logonId"
                control={control}
                render={({ field }) => (
                  <CatInputField
                    type="text"
                    label={t('LOGIN_ID_CSR')}
                    name="logonId"
                    value={field.value}
                    onBlChange={(e) => field.onChange(e.detail.value)}
                    className="mb-3"
                    size="md"
                    data-testid="logonId"
                  />
                )}
              />
              <Controller
                name="companyName"
                control={control}
                render={({ field }) => (
                  <CatInputField
                    type="text"
                    label={t('CAT_EMAIL_COMPANYNAME')}
                    name="companyName"
                    value={field.value}
                    onBlChange={(e) => field.onChange(e.detail.value)}
                    className="mb-3"
                    size="md"
                    data-testid="companyName"
                  />
                )}
              />
              <CountryInput
                control={control}
                t={t}
                countries={countries}
                onSelectCountry={handleSelectCountry}
              />
            </div>
          </div>
          <div className="row">
            <div className="col-12 text-end mt-3">
              <CatButton
                type="button"
                variant="ghost"
                onClick={resetForm}
                disabled={isSearching}
                className="me-2"
                data-testid="cat-button"
              >
                {t('CLEAR_FILTERS')}
              </CatButton>
              <CatButton
                type="submit"
                variant="primary"
                disabled={isSearching}
                data-testid="search-submit-button"
                className="px-6"
                size="sm"
              >
                {t('SEARCH_CSR')}
              </CatButton>
            </div>
          </div>
        </form>
      </FormProvider>

      {isSearching && <OrderSearchTableLoader />}

      {error && (
        <ErrorFeedBackComponent errorTypeLevel="table" errorCode={errorCode} />
      )}

      {!isSearching && !error && hasSubmittedSearch && (
        <CustomerSearchResults
          searchResults={searchResults}
          totalCount={totalCount}
          currentPage={currentPage}
          pageSize={pageSize}
          setCurrentPage={setCurrentPage}
          setPageSize={setPageSize}
          onSort={handleSort}
          onPaginationChange={performPaginationSearch}
          isCsrUser={isCsrUser}
        />
      )}
    </>
  );
};

export default React.memo(CustomerSearchForm);
