import { type NextRequest } from 'next/server';
import { createChildLogger } from 'logger';
import { ENTRY, EXIT, ERROR } from 'constants/logging';
import { getRequestId } from 'utils/requestId';
import { fetchDealerAssociation } from 'services/dealer-association/dealer-association.server';
import { getEsite } from 'services/esite';
import { err as serializer } from 'pino-std-serializers';

export async function GET(req: NextRequest) {
  const query = req.nextUrl.searchParams;
  const onBehalfofUserId = query.get('onBehalfofUserId');
  const isCsrFlow = query.get('isCsrFlow') === 'true';
  const pccRequestId = getRequestId(req.headers);

  const childLogger = createChildLogger({
    msgPrefix: 'Fetch Dealer Associations Handler',
    pccRequestId
  });

  childLogger.trace(ENTRY);
  childLogger.debug({ onBehalfofUserId, isCsrFlow });

  try {
    const { storeId, locale } = await getEsite();
    
    // Build the URL with CSR parameters if needed
    let apiUrl = `/eCommerce/me/dealerAssociations?storeId=${storeId}&locale=${locale}`;
    
    if (isCsrFlow && onBehalfofUserId) {
      apiUrl += `&onBehalfofUserId=${onBehalfofUserId}`;
    }

    const response = await fetch(`${process.env.COMMERCE_API_HOST}${apiUrl}`, {
      method: 'GET',
      headers: {
        'Authorization': req.headers.get('authorization') || '',
        'Cookie': req.headers.get('cookie') || '',
        'WCToken': 'WCToken',
        'WCTrustedToken': 'WCTrustedToken'
      }
    });

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    // Process the response to extract potential account issues
    let potentialAccountIssues: string[] = [];
    
    if (data.errors && data.errors.length > 0) {
      potentialAccountIssues = data.errors.map((error: any) => error.message || error.description);
    }
    
    // If there are account issues but still associations, include both
    const result = {
      associations: data.associations || [],
      potentialAccountIssues: potentialAccountIssues.length > 0 ? potentialAccountIssues : null
    };

    childLogger.trace(EXIT);
    return Response.json(result);
  } catch (error) {
    childLogger.error(serializer(error), ERROR);
    childLogger.trace(EXIT);
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to fetch dealer associations',
        message: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
