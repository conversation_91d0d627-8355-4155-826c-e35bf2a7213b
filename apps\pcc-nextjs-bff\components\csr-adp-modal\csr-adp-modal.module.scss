.csrAdpModal {
  .closeButton {
    border: none;
    background: transparent;
    padding: 0.25rem;
    
    &:hover {
      background-color: var(--cat-color-neutral-10);
      border-radius: 0.25rem;
    }
  }
}

.modalBody {
  padding: 1.5rem;
  min-height: 200px;
  position: relative;
}

.dealerList {
  border: 1px solid var(--cat-color-neutral-20);
  border-radius: 0.375rem;
  padding: 1rem;
  background-color: var(--cat-color-neutral-5);
}

.dealerItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--cat-color-neutral-15);
  
  &:last-child {
    border-bottom: none;
  }
}

.customerInfo {
  border: 1px solid var(--cat-color-neutral-20);
  border-radius: 0.375rem;
  padding: 1rem;
  background-color: var(--cat-color-neutral-5);
  
  p {
    margin-bottom: 0.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
