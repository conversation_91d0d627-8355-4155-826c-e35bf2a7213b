.wrapText {
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
}

.clickableLogonId {
  cursor: pointer;
  color: var(--cat-color-primary-60);
  text-decoration: underline;

  &:hover {
    color: var(--cat-color-primary-70);
    text-decoration: none;
  }

  &:focus {
    outline: 2px solid var(--cat-color-primary-60);
    outline-offset: 2px;
    border-radius: 2px;
  }
}

.catPagination::part(dropdown__body) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  width: 65px;
}
.catPagination::part(cat_c_pagination_dropdown) {
  flex-basis: 116px;
}
.customersearch-list-table-header {
  position: relative;
  z-index: 1;
}
.customer-search-header-cell {
  &::part(base) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px;
    margin-left: 0px;
  }
  &::part(button) {
    justify-content: center;
  }
}
