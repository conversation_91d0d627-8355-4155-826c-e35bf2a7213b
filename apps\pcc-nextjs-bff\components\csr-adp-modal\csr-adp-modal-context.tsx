'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { DealerAssociationResponse } from 'services/dealer-association';

interface DealerAssociation {
  dealerName: string;
  storeLocationCode: string;
  storeId: string;
}

interface DealerCustomerInfo {
  customerNumber: string;
  customerName: string;
  email?: string;
  phone?: string;
  address?: {
    city?: string;
    state?: string;
    country?: string;
  };
}

interface CsrAdpModalContextType {
  isModalOpen: boolean;
  isLoading: boolean;
  error: string | null;
  dealerAssociations: DealerAssociation[] | null;
  dealerCustomerInfo: DealerCustomerInfo | null;
  potentialAccountIssues: string[] | null;
  selectedCustomer: {
    logonId: string;
    userId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null;
  
  // Actions
  openModal: (customer: {
    logonId: string;
    userId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  }) => void;
  closeModal: () => void;
  fetchDealerAssociations: (onBehalfofUserId: string) => Promise<void>;
  fetchDealerCustomerInfo: (customerNumber: string) => Promise<void>;
  submitAdp: (onBehalfofUserId: string) => Promise<void>;
  disconnectUser: () => Promise<void>;
  setError: (error: string | null) => void;
}

const CsrAdpModalContext = createContext<CsrAdpModalContextType | undefined>(undefined);

interface CsrAdpModalProviderProps {
  children: ReactNode;
}

export const CsrAdpModalProvider: React.FC<CsrAdpModalProviderProps> = ({ children }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dealerAssociations, setDealerAssociations] = useState<DealerAssociation[] | null>(null);
  const [dealerCustomerInfo, setDealerCustomerInfo] = useState<DealerCustomerInfo | null>(null);
  const [potentialAccountIssues, setPotentialAccountIssues] = useState<string[] | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<{
    logonId: string;
    userId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null>(null);

  const openModal = useCallback((customer: {
    logonId: string;
    userId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  }) => {
    setSelectedCustomer(customer);
    setIsModalOpen(true);
    setError(null);
    // Reset previous data
    setDealerAssociations(null);
    setDealerCustomerInfo(null);
    setPotentialAccountIssues(null);
  }, []);

  const closeModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedCustomer(null);
    setError(null);
    setDealerAssociations(null);
    setDealerCustomerInfo(null);
    setPotentialAccountIssues(null);
  }, []);

  const fetchDealerAssociations = useCallback(async (onBehalfofUserId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/dealer-associations?onBehalfofUserId=${onBehalfofUserId}&isCsrFlow=true`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch dealer associations');
      }
      
      const data = await response.json();
      
      if (data.associations) {
        setDealerAssociations(data.associations);
      }
      
      // Check for potential account issues
      if (data.potentialAccountIssues && data.potentialAccountIssues.length > 0) {
        setPotentialAccountIssues(data.potentialAccountIssues);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dealer associations');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchDealerCustomerInfo = useCallback(async (customerNumber: string) => {
    try {
      const response = await fetch(`/api/dealer-customers/${customerNumber}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch dealer customer info');
      }
      
      const data = await response.json();
      setDealerCustomerInfo(data);
    } catch (err) {
      console.error('Failed to fetch dealer customer info:', err);
      // Don't set error for this as it's not critical
    }
  }, []);

  const submitAdp = useCallback(async (onBehalfofUserId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // First submit ADP
      const adpResponse = await fetch('/api/submit-adp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          onBehalfofUserId,
          isCsrFlow: true
        }),
      });
      
      if (!adpResponse.ok) {
        throw new Error('Failed to submit ADP');
      }
      
      // Then set dealer customer organization in session
      const sessionResponse = await fetch('/api/dealer-customer-org-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          onBehalfofUserId
        }),
      });
      
      if (!sessionResponse.ok) {
        throw new Error('Failed to set dealer customer organization in session');
      }
      
      // Redirect to dealer esite (this will be handled by the server response)
      const redirectData = await sessionResponse.json();
      if (redirectData.redirectUrl) {
        window.location.href = redirectData.redirectUrl;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit ADP');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const disconnectUser = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/user-reset-session');
      
      if (!response.ok) {
        throw new Error('Failed to disconnect user');
      }
      
      // Redirect to CSR home page
      window.location.href = '/ccs-home';
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect user');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const contextValue: CsrAdpModalContextType = {
    isModalOpen,
    isLoading,
    error,
    dealerAssociations,
    dealerCustomerInfo,
    potentialAccountIssues,
    selectedCustomer,
    openModal,
    closeModal,
    fetchDealerAssociations,
    fetchDealerCustomerInfo,
    submitAdp,
    disconnectUser,
    setError
  };

  return (
    <CsrAdpModalContext.Provider value={contextValue}>
      {children}
    </CsrAdpModalContext.Provider>
  );
};

export const useCsrAdpModal = (): CsrAdpModalContextType => {
  const context = useContext(CsrAdpModalContext);
  if (context === undefined) {
    throw new Error('useCsrAdpModal must be used within a CsrAdpModalProvider');
  }
  return context;
};
