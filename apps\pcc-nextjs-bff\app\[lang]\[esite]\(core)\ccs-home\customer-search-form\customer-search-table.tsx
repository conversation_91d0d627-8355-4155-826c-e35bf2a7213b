'use client';
import { Conditional } from '@cat-ecom/pcc-components';
import {
  CatTableBody,
  CatTableCell,
  CatTableRow,
  CatTextLink
} from 'components/blocks-components';
import { useCsrAdpModal } from 'components/csr-adp-modal/csr-adp-modal-context';
import styles from './customer-search-table.module.scss';

interface CustomerSearchTableProps {
  paginatedCustomers: any[];
  isCsrUser?: boolean;
}

export const CustomerSearchTable = ({
  paginatedCustomers,
  isCsrUser = false
}: CustomerSearchTableProps) => {
  const { openModal } = useCsrAdpModal();

  const handleCustomerClick = (customer: any) => {
    if (isCsrUser && customer.logonId) {
      openModal({
        logonId: customer.logonId,
        userId: customer.id || customer.userId,
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email || customer.address?.email
      });
    }
  };

  const formatName = (firstName, lastName) => {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (firstName) {
      return firstName;
    } else if (lastName) {
      return lastName;
    }
    return '-';
  };

  const formattedDate = (isoString: string) => {
    return new Date(isoString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit'
    });
  };

  const displayProfileAddress = (city: string, state: string) => {
    if (city && state) {
      return `${city},${state}`;
    } else if (city) {
      return city;
    } else if (state) {
      return state;
    }
    return '-';
  };

  return (
    <CatTableBody className="w-100">
      <Conditional test={paginatedCustomers.length > 0}>
        {paginatedCustomers.map((customer) => {
          return (
            <CatTableRow
              key={customer.id || customer.email || customer.logonId}
            >
              <CatTableCell width="auto" isFrozen className={styles.wrapText}>
                {isCsrUser && customer?.logonId ? (
                  <CatTextLink
                    onClick={() => handleCustomerClick(customer)}
                    className={styles.clickableLogonId}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleCustomerClick(customer);
                      }
                    }}
                  >
                    {customer.logonId}
                  </CatTextLink>
                ) : (
                  <span>{customer?.logonId ?? '-'}</span>
                )}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {formatName(customer?.firstName, customer?.lastName)}
              </CatTableCell>
              <CatTableCell
                width="auto"
                className={styles.wrapText}
                align="center"
              >
                {customer?.address?.phone ?? '-'}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {customer?.address?.email ?? '-'}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {customer?.companyName ?? '-'}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {displayProfileAddress(
                  customer?.address?.city,
                  customer?.address?.state
                )}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {customer?.lastActiveDateTime
                  ? formattedDate(customer?.lastActiveDateTime)
                  : '-'}
              </CatTableCell>
            </CatTableRow>
          );
        })}
      </Conditional>
    </CatTableBody>
  );
};
