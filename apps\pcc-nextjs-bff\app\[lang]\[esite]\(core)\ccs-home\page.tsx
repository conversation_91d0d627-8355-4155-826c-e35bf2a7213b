import { getParams } from 'app/[lang]/[esite]/utils/headers';
import cx from 'classnames';
import { CatHeading, CatLayoutContainer } from 'components/blocks-components';
import { IdleTimeoutWrapper } from 'components/IdleTimeoutWrapper';
import {
  CSR_LEGACY_LANDING_PAGE_ROUTE,
  HEADLESS_HOME_PAGE
} from 'constants/links';
import { redirect } from 'next/navigation';
import { TranslationsProvider } from 'providers/translations-provider';
import { getCountriesStates } from 'services/countries-and-states/countries-and-states';
import { getEsite } from 'services/esite';
import { getClient } from 'services/launch-darkly';
import { getUser } from 'services/user';
import { getSeoUrl, normalizeCommerceLink } from 'utils/links';
import { getRelativeURLWithParams } from 'utils/url';
import { PCC_RELEASE_CAT_CUSTOMER_CARE_HOME_AND_SEARCH } from './_constants/feature-flags';
import { cccHomePageKeys } from './_constants/translation-keys';
import CSRSearchForms from './csr-search-home';
import styles from './csr.module.scss';
import { CsrAdpModalProvider } from 'components/csr-adp-modal';
import CsrAdpModalWrapper from 'components/csr-adp-modal/csr-adp-modal-wrapper';
import { csrAdpModalKeys } from 'components/csr-adp-modal/translation-keys';
import { assertFaultInjection } from 'utils/faultInjection';
import {
  FAULTINJECT_CSR_GENERIC_ERROR,
  FAULTINJECT_CSR_PAGE
} from 'services/ccs-home/constants';
import { ErrorFeedBackComponent } from './error-feedback-component/error-feedback-component';

const translationKeys = [...cccHomePageKeys, ...csrAdpModalKeys];

export default async function CSRHomePage() {
  const {
    firstName,
    userAccessFlags: { isCatCsrUser, isLoggedIn }
  } = await getUser();

  const params = getParams();
  const { storeId, langId, catalogId } = await getEsite();
  const { esite } = params;

  // Get Launch Darkly client and check feature flag
  const ldClient = await getClient();
  const context = {
    kind: 'location',
    key: esite,
    esite
  };
  const isCSRHomePageEnabled = await ldClient.variation(
    PCC_RELEASE_CAT_CUSTOMER_CARE_HOME_AND_SEARCH,
    context,
    false
  );

  // Redirect to legacy page if feature flag is not enabled
  if (!isCSRHomePageEnabled) {
    const legacyUrl = normalizeCommerceLink(
      getRelativeURLWithParams(CSR_LEGACY_LANDING_PAGE_ROUTE, {
        storeId,
        langId,
        catalogId
      })
    );
    redirect(legacyUrl);
  }

  // Redirect non-CSR users to home page
  if (!isLoggedIn || !isCatCsrUser) {
    redirect(normalizeCommerceLink(getSeoUrl(params, HEADLESS_HOME_PAGE)));
  }

  // Get CSR's full name for the welcome message
  const csrName = `${firstName}`.trim();
  const welcomeMessage = `${'Welcome'}, ${csrName}`;

  const { countries } = await getCountriesStates();

  let hasFaultInjection = false;
  try {
    assertFaultInjection(
      FAULTINJECT_CSR_PAGE,
      FAULTINJECT_CSR_GENERIC_ERROR,
      new Error('Handling default API failure')
    );
  } catch (err) {
    hasFaultInjection = true;
  }

  return (
    <TranslationsProvider keys={translationKeys}>
      <IdleTimeoutWrapper>
        <CatLayoutContainer>
          {hasFaultInjection ? (
            <div className={styles.container}>
              <div className={styles.errordetail}>
                <CatHeading
                  decorator
                  level="h2"
                  variant="headline"
                  className={cx('d-print-none', styles['csrHeadline'])}
                >
                  {'Cat Customer Care Home'}
                </CatHeading>
              </div>
              <ErrorFeedBackComponent errorTypeLevel="page" />
            </div>
          ) : (
            <>
              <div className="my-5">
                <CatHeading
                  decorator
                  level="h2"
                  variant="headline"
                  className={cx('d-print-none', styles['csrHeadline'])}
                >
                  {'Cat Customer Care Home'}
                </CatHeading>
                <div className="mt-4">
                  <h5 className={styles['csrHomeWelcomeLine']}>
                    {welcomeMessage}
                  </h5>
                  <p className={styles['searchSubtitle']}>
                    {'You may search by customer or by order.'}
                  </p>
                </div>
              </div>
              <CsrAdpModalProvider>
                <CSRSearchForms
                  countries={countries}
                  isCsrUser={isCatCsrUser}
                />
                <CsrAdpModalWrapper />
              </CsrAdpModalProvider>
            </>
          )}
        </CatLayoutContainer>
      </IdleTimeoutWrapper>
    </TranslationsProvider>
  );
}
