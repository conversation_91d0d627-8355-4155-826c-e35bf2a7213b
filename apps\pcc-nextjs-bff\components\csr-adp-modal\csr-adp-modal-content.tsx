'use client';

import React, { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import {
  CatModal,
  CatButton,
  CatIconClose,
  CatHeading,
  CatInlineNotification,
  CatIconWarningTriangle
} from 'components/blocks-components';
import { Conditional } from 'components/pcc-components';
import LoaderOverlay from 'components/loader-overlay';
import { useCsrAdpModal } from './csr-adp-modal-context';
import styles from './csr-adp-modal.module.scss';

interface CsrAdpModalContentProps {
  selectedCustomer: {
    logonId: string;
    userId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null;
}

export const CsrAdpModalContent: React.FC<CsrAdpModalContentProps> = ({
  selectedCustomer
}) => {
  const t = useTranslations();
  const {
    isModalOpen,
    closeModal,
    isLoading,
    dealerAssociations,
    dealerCustomerInfo,
    potentialAccountIssues,
    submitAdp,
    disconnectUser,
    fetchDealerAssociations,
    fetchDealerCustomerInfo
  } = useCsrAdpModal();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  useEffect(() => {
    if (isModalOpen && selectedCustomer) {
      // Fetch dealer associations and customer info when modal opens
      fetchDealerAssociations(selectedCustomer.userId);
      fetchDealerCustomerInfo(selectedCustomer.logonId);
    }
  }, [isModalOpen, selectedCustomer, fetchDealerAssociations, fetchDealerCustomerInfo]);

  const handleSubmitAdp = async () => {
    if (!selectedCustomer) return;
    
    setIsSubmitting(true);
    try {
      await submitAdp(selectedCustomer.userId);
      // On successful ADP submission, user will be redirected to dealer esite
    } catch (error) {
      console.error('ADP submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDisconnect = async () => {
    setIsDisconnecting(true);
    try {
      await disconnectUser();
      closeModal();
      // User will be redirected to CSR home page
    } catch (error) {
      console.error('Disconnect failed:', error);
    } finally {
      setIsDisconnecting(false);
    }
  };

  const handleCloseModal = () => {
    closeModal();
  };

  if (!selectedCustomer) {
    return null;
  }

  const customerName = selectedCustomer.firstName && selectedCustomer.lastName 
    ? `${selectedCustomer.firstName} ${selectedCustomer.lastName}`
    : selectedCustomer.logonId;

  return (
    <CatModal
      isActive={isModalOpen}
      onBlModalClose={handleCloseModal}
      size="lg"
      className={styles.csrAdpModal}
    >
      <div slot="header" className="d-flex justify-content-between align-items-center">
        <CatHeading variant="title">
          {t('CSR_ADP_MODAL_TITLE', { customerName })}
        </CatHeading>
        <CatButton
          variant="ghost"
          size="sm"
          onClick={handleCloseModal}
          className={styles.closeButton}
          aria-label={t('CLOSE')}
        >
          <CatIconClose />
        </CatButton>
      </div>

      <div className={styles.modalBody}>
        <Conditional test={isLoading}>
          <LoaderOverlay />
        </Conditional>

        <Conditional test={!isLoading}>
          <div className="mb-4">
            <p className="mb-3">
              {t('CSR_ADP_MODAL_DESCRIPTION', { 
                customerName,
                logonId: selectedCustomer.logonId 
              })}
            </p>

            {/* Display potential account issues if any */}
            <Conditional test={potentialAccountIssues && potentialAccountIssues.length > 0}>
              <CatInlineNotification variant="warning" className="mb-3">
                <CatIconWarningTriangle slot="before" />
                <div>
                  <strong>{t('CSR_ADP_POTENTIAL_ACCOUNT_ISSUES')}</strong>
                  <ul className="mt-2 mb-0">
                    {potentialAccountIssues?.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </div>
              </CatInlineNotification>
            </Conditional>

            {/* Display dealer associations */}
            <Conditional test={dealerAssociations && dealerAssociations.length > 0}>
              <div className="mb-3">
                <h6>{t('CSR_ADP_DEALER_ASSOCIATIONS')}</h6>
                <div className={styles.dealerList}>
                  {dealerAssociations?.map((dealer, index) => (
                    <div key={index} className={styles.dealerItem}>
                      <strong>{dealer.dealerName}</strong>
                      <span className="text-muted ms-2">{dealer.storeLocationCode}</span>
                    </div>
                  ))}
                </div>
              </div>
            </Conditional>

            {/* Display customer information */}
            <Conditional test={dealerCustomerInfo}>
              <div className="mb-3">
                <h6>{t('CSR_ADP_CUSTOMER_INFO')}</h6>
                <div className={styles.customerInfo}>
                  <p><strong>{t('CSR_ADP_CUSTOMER_NUMBER')}:</strong> {dealerCustomerInfo?.customerNumber}</p>
                  <p><strong>{t('CSR_ADP_CUSTOMER_NAME')}:</strong> {dealerCustomerInfo?.customerName}</p>
                  <Conditional test={dealerCustomerInfo?.email}>
                    <p><strong>{t('Email address')}:</strong> {dealerCustomerInfo?.email}</p>
                  </Conditional>
                </div>
              </div>
            </Conditional>
          </div>
        </Conditional>
      </div>

      <div slot="footer" className="d-flex justify-content-end gap-2">
        <CatButton
          variant="ghost"
          onClick={handleDisconnect}
          disabled={isSubmitting || isDisconnecting || isLoading}
          className="me-2"
        >
          {isDisconnecting ? t('CSR_ADP_DISCONNECTING') : t('CSR_ADP_DISCONNECT')}
        </CatButton>
        <CatButton
          variant="primary"
          onClick={handleSubmitAdp}
          disabled={isSubmitting || isDisconnecting || isLoading}
        >
          {isSubmitting ? t('CSR_ADP_CONNECTING') : t('CSR_ADP_CONNECT')}
        </CatButton>
      </div>
    </CatModal>
  );
};

export default CsrAdpModalContent;
